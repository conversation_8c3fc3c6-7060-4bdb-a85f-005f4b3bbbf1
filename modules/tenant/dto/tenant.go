package dto

import "time"

// Pagination chứa thông tin phân trang
type Pagination struct {
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}

// Tenant DTOs
// ------------------------------

// CreateTenantRequest chứa thông tin để tạo tenant mới
type CreateTenantRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	PlanID      int    `json:"plan_id" binding:"required"`
	OwnerUserID int64  `json:"owner_user_id" binding:"required"`
	Logo        string `json:"logo" binding:"omitempty"`
}

// UpdateTenantRequest chứa thông tin để cập nhật tenant
type UpdateTenantRequest struct {
	Name        *string `json:"name" binding:"omitempty,max=100"`
	Description *string `json:"description" binding:"omitempty,max=500"`
	PlanID      *int    `json:"plan_id" binding:"omitempty"`
	IsActive    *bool   `json:"is_active" binding:"omitempty"`
	Logo        *string `json:"logo" binding:"omitempty"`
	Settings    *string `json:"settings" binding:"omitempty"`
}

// TenantResponse chứa thông tin trả về về tenant
type TenantResponse struct {
	ID             int64      `json:"id"`
	Name           string     `json:"name"`
	Slug           string     `json:"slug"`
	Description    string     `json:"description"`
	PlanID         int        `json:"plan_id"`
	PlanName       string     `json:"plan_name,omitempty"`
	OwnerUserID    int64      `json:"owner_user_id"`
	IsActive       bool       `json:"is_active"`
	Logo           string     `json:"logo"`
	StorageUsed    int64      `json:"storage_used"`
	StorageLimit   int64      `json:"storage_limit"`
	BandwidthUsed  int64      `json:"bandwidth_used"`
	BandwidthLimit int64      `json:"bandwidth_limit"`
	ExpiresAt      *time.Time `json:"expires_at,omitempty"`
	Settings       string     `json:"settings"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// ListTenantsParams chứa các tham số để lọc và phân trang danh sách tenant
type ListTenantsParams struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy     string `form:"sort_by" binding:"omitempty"`
	SortOrder  string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Search     string `form:"search" binding:"omitempty"`
	PlanID     *int   `form:"plan_id" binding:"omitempty"`
	OwnerID    *int64 `form:"owner_id" binding:"omitempty"`
	ActiveOnly bool   `form:"active_only" binding:"omitempty"`
}

// ListTenantsResponse chứa kết quả danh sách tenant có phân trang
type ListTenantsResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Pagination Pagination       `json:"pagination"`
}

// UserTenantsResponse chứa danh sách tenant của một người dùng
type UserTenantsResponse struct {
	Tenants []TenantResponse `json:"tenants"`
	Count   int              `json:"count"`
}

// TenantUser DTOs
// ------------------------------

// AddUserRequest chứa thông tin để thêm người dùng vào tenant
type AddUserRequest struct {
	TenantID int64  `json:"tenant_id" binding:"required"`
	UserID   int64  `json:"user_id" binding:"required"`
	Role     string `json:"role" binding:"required,oneof=owner admin member viewer"`
}

// TenantUserResponse chứa thông tin về mối quan hệ giữa tenant và user
type TenantUserResponse struct {
	ID        int64     `json:"id"`
	TenantID  int64     `json:"tenant_id"`
	UserID    int64     `json:"user_id"`
	Role      string    `json:"role"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TenantUsersResponse chứa danh sách người dùng của một tenant
type TenantUsersResponse struct {
	Users []TenantUserResponse `json:"users"`
	Count int                  `json:"count"`
}

// Plan DTOs
// ------------------------------

// CreatePlanRequest chứa thông tin để tạo gói dịch vụ mới
type CreatePlanRequest struct {
	Name           string   `json:"name" binding:"required,max=100"`
	Description    string   `json:"description" binding:"max=500"`
	Price          float64  `json:"price" binding:"required,min=0"`
	PriceYearly    float64  `json:"price_yearly" binding:"omitempty,min=0"`
	Currency       string   `json:"currency" binding:"omitempty,len=3"`
	MaxUsers       int      `json:"max_users" binding:"required,min=1"`
	StorageLimit   int64    `json:"storage_limit" binding:"required,min=0"`
	BandwidthLimit int64    `json:"bandwidth_limit" binding:"required,min=0"`
	Features       string   `json:"features" binding:"omitempty"`
	IsActive       bool     `json:"is_active" binding:"omitempty"`
	IsPublic       bool     `json:"is_public" binding:"omitempty"`
}

// UpdatePlanRequest chứa thông tin để cập nhật gói dịch vụ
type UpdatePlanRequest struct {
	Name           *string  `json:"name" binding:"omitempty,max=100"`
	Description    *string  `json:"description" binding:"omitempty,max=500"`
	Price          *float64 `json:"price" binding:"omitempty,min=0"`
	PriceYearly    *float64 `json:"price_yearly" binding:"omitempty,min=0"`
	Currency       *string  `json:"currency" binding:"omitempty,len=3"`
	MaxUsers       *int     `json:"max_users" binding:"omitempty,min=1"`
	StorageLimit   *int64   `json:"storage_limit" binding:"omitempty,min=0"`
	BandwidthLimit *int64   `json:"bandwidth_limit" binding:"omitempty,min=0"`
	Features       *string  `json:"features" binding:"omitempty"`
	IsActive       *bool    `json:"is_active" binding:"omitempty"`
	IsPublic       *bool    `json:"is_public" binding:"omitempty"`
}

// PlanResponse chứa thông tin trả về về gói dịch vụ
type PlanResponse struct {
	ID             int       `json:"id"`
	Name           string    `json:"name"`
	Description    string    `json:"description"`
	Price          float64   `json:"price"`
	PriceYearly    float64   `json:"price_yearly"`
	Currency       string    `json:"currency"`
	MaxUsers       int       `json:"max_users"`
	StorageLimit   int64     `json:"storage_limit"`
	BandwidthLimit int64     `json:"bandwidth_limit"`
	Features       string    `json:"features"`
	IsActive       bool      `json:"is_active"`
	IsPublic       bool      `json:"is_public"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ListPlansParams chứa các tham số để lọc và phân trang danh sách gói dịch vụ
type ListPlansParams struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy     string `form:"sort_by" binding:"omitempty"`
	SortOrder  string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Search     string `form:"search" binding:"omitempty"`
	ActiveOnly bool   `form:"active_only" binding:"omitempty"`
	PublicOnly bool   `form:"public_only" binding:"omitempty"`
}

// ListPlansResponse chứa kết quả danh sách gói dịch vụ có phân trang
type ListPlansResponse struct {
	Plans      []PlanResponse `json:"plans"`
	Pagination Pagination     `json:"pagination"`
}

// ActivePlansResponse chứa danh sách gói dịch vụ đang hoạt động
type ActivePlansResponse struct {
	Plans []PlanResponse `json:"plans"`
}
