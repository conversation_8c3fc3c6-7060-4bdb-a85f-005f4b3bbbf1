package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"

	"github.com/gosimple/slug"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// Tenant methods
// ---------------------------------

// CreateTenant tạo tenant mới
func (r *mysqlRepository) CreateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_tenants")
	defer span.End()

	// Tạo slug từ tên
	if tenant.Slug == "" {
		tenant.Slug = slug.Make(tenant.Name)
	}

	result := r.db.WithContext(ctx).Create(tenant)
	if result.Error != nil {
		r.logger.Error("Không thể tạo tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetTenantByID lấy tenant theo ID
func (r *mysqlRepository) GetTenantByID(ctx context.Context, id int64) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).First(&tenant, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// GetTenantBySlug lấy tenant theo slug
func (r *mysqlRepository) GetTenantBySlug(ctx context.Context, slug string) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).Where("slug = ?", slug).First(&tenant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant theo slug", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// UpdateTenant cập nhật tenant
func (r *mysqlRepository) UpdateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_tenants")
	defer span.End()

	// Tạo slug từ tên nếu cần
	if tenant.Slug == "" && tenant.Name != "" {
		tenant.Slug = slug.Make(tenant.Name)
	}

	result := r.db.WithContext(ctx).Save(tenant)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrTenantNotFound
	}

	return nil
}

// DeleteTenant xóa tenant
func (r *mysqlRepository) DeleteTenant(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_tenants")
	defer span.End()

	// Sử dụng transaction để đảm bảo xóa cả tenant users
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		r.logger.Error("Không thể bắt đầu transaction", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return internal.ErrDatabaseError
	}

	// Xóa tenant users trước
	if err := tx.Where("tenant_id = ?", id).Delete(&internal.TenantUser{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant users", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	// Xóa tenant
	result := tx.Delete(&internal.Tenant{}, id)
	if result.Error != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return internal.ErrTenantNotFound
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Không thể commit transaction", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	return nil
}

// ListTenants lấy danh sách tenant với phân trang và lọc
func (r *mysqlRepository) ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]internal.Tenant, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Tenant{})

	// Áp dụng các điều kiện lọc
	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	if params.PlanID != nil {
		query = query.Where("plan_id = ?", *params.PlanID)
	}

	if params.OwnerID != nil {
		query = query.Where("owner_user_id = ?", *params.OwnerID)
	}

	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var tenants []internal.Tenant
	if err := query.Find(&tenants).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return tenants, total, nil
}

// GetTenantsByUserID lấy các tenant mà user là thành viên
func (r *mysqlRepository) GetTenantsByUserID(ctx context.Context, userID int64) ([]internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenants []internal.Tenant

	// Tìm tất cả các tenant mà user là thành viên
	err := r.db.WithContext(ctx).
		Joins("JOIN tenant_users ON tenant_users.tenant_id = tenant_tenants.id").
		Where("tenant_users.user_id = ? AND tenant_users.is_active = ?", userID, true).
		Find(&tenants).Error

	if err != nil {
		r.logger.Error("Không thể lấy tenant của user", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, internal.ErrDatabaseError
	}

	return tenants, nil
}

// Tenant user methods
// ---------------------------------

// AddUserToTenant thêm người dùng vào tenant
func (r *mysqlRepository) AddUserToTenant(ctx context.Context, tenantUser *internal.TenantUser) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_users")
	defer span.End()

	// Kiểm tra xem đã tồn tại chưa
	var existingCount int64
	err := r.db.WithContext(ctx).Model(&internal.TenantUser{}).
		Where("tenant_id = ? AND user_id = ?", tenantUser.TenantID, tenantUser.UserID).
		Count(&existingCount).Error

	if err != nil {
		r.logger.Error("Không thể kiểm tra tenant user", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	if existingCount > 0 {
		return internal.ErrUserAlreadyMember
	}

	// Thêm người dùng vào tenant
	result := r.db.WithContext(ctx).Create(tenantUser)
	if result.Error != nil {
		r.logger.Error("Không thể thêm user vào tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// RemoveUserFromTenant xóa người dùng khỏi tenant
func (r *mysqlRepository) RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).Where("tenant_id = ? AND user_id = ?", tenantID, userID).Delete(&internal.TenantUser{})
	if result.Error != nil {
		r.logger.Error("Không thể xóa user khỏi tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrUserNotFound
	}

	return nil
}

// UpdateUserRole cập nhật vai trò của người dùng trong tenant
func (r *mysqlRepository) UpdateUserRole(ctx context.Context, tenantID, userID int64, role string) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).
		Model(&internal.TenantUser{}).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Update("role", role)

	if result.Error != nil {
		r.logger.Error("Không thể cập nhật vai trò của user", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrUserNotFound
	}

	return nil
}

// GetTenantUsers lấy danh sách người dùng của tenant
func (r *mysqlRepository) GetTenantUsers(ctx context.Context, tenantID int64) ([]internal.TenantUser, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenantUsers []internal.TenantUser
	result := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at").
		Find(&tenantUsers)

	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách user của tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return tenantUsers, nil
}

// GetUserRole lấy vai trò của người dùng trong tenant
func (r *mysqlRepository) GetUserRole(ctx context.Context, tenantID, userID int64) (string, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenantUser internal.TenantUser
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		First(&tenantUser)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return "", internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy vai trò của user", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return "", internal.ErrDatabaseError
	}

	return tenantUser.Role, nil
}

// Plan methods
// ---------------------------------

// CreatePlan tạo gói dịch vụ mới
func (r *mysqlRepository) CreatePlan(ctx context.Context, plan *internal.Plan) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_plans")
	defer span.End()

	result := r.db.WithContext(ctx).Create(plan)
	if result.Error != nil {
		r.logger.Error("Không thể tạo gói dịch vụ", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetPlanByID lấy gói dịch vụ theo ID
func (r *mysqlRepository) GetPlanByID(ctx context.Context, id int) (*internal.Plan, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	var plan internal.Plan
	result := r.db.WithContext(ctx).First(&plan, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPlanNotFound
		}
		r.logger.Error("Không thể lấy gói dịch vụ", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &plan, nil
}

// UpdatePlan cập nhật gói dịch vụ
func (r *mysqlRepository) UpdatePlan(ctx context.Context, plan *internal.Plan) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_plans")
	defer span.End()

	result := r.db.WithContext(ctx).Save(plan)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật gói dịch vụ", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPlanNotFound
	}

	return nil
}

// DeletePlan xóa gói dịch vụ
func (r *mysqlRepository) DeletePlan(ctx context.Context, id int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_plans")
	defer span.End()

	// Kiểm tra xem có tenant nào đang sử dụng gói này không
	var count int64
	err := r.db.WithContext(ctx).Model(&internal.Tenant{}).Where("plan_id = ?", id).Count(&count).Error
	if err != nil {
		r.logger.Error("Không thể kiểm tra tenant sử dụng gói", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	if count > 0 {
		// Có tenant đang sử dụng, không thể xóa
		// Thay vào đó, đánh dấu là không hoạt động
		result := r.db.WithContext(ctx).Model(&internal.Plan{}).Where("id = ?", id).Update("is_active", false)
		if result.Error != nil {
			r.logger.Error("Không thể đánh dấu gói dịch vụ không hoạt động", logger.String("error", result.Error.Error()))
			tracing.RecordError(ctx, result.Error)
			return internal.ErrDatabaseError
		}

		if result.RowsAffected == 0 {
			return internal.ErrPlanNotFound
		}

		return nil
	}

	// Không có tenant nào sử dụng, xóa gói
	result := r.db.WithContext(ctx).Delete(&internal.Plan{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa gói dịch vụ", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPlanNotFound
	}

	return nil
}

// ListPlans lấy danh sách gói dịch vụ với phân trang và lọc
func (r *mysqlRepository) ListPlans(ctx context.Context, params dto.ListPlansParams) ([]internal.Plan, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Plan{})

	// Áp dụng các điều kiện lọc
	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	if params.PublicOnly {
		query = query.Where("is_public = ?", true)
	}

	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số gói dịch vụ", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "price"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var plans []internal.Plan
	if err := query.Find(&plans).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách gói dịch vụ", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return plans, total, nil
}

// GetActivePlans lấy tất cả các gói dịch vụ đang hoạt động
func (r *mysqlRepository) GetActivePlans(ctx context.Context) ([]internal.Plan, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	var plans []internal.Plan
	result := r.db.WithContext(ctx).
		Where("is_active = ? AND is_public = ?", true, true).
		Order("price asc").
		Find(&plans)

	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách gói dịch vụ hoạt động", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return plans, nil
}
