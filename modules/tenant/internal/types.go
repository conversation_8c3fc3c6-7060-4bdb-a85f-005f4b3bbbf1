package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/tenant/dto"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrTenantNotFound là lỗi khi không tìm thấy tenant
	ErrTenantNotFound ServiceError = "tenant_not_found"
	// ErrTenantAlreadyExists là lỗi khi tenant đã tồn tại
	ErrTenantAlreadyExists ServiceError = "tenant_already_exists"
	// ErrPlanNotFound là lỗi khi không tìm thấy plan
	ErrPlanNotFound ServiceError = "plan_not_found"
	// ErrUserNotFound là lỗi khi không tìm thấy người dùng
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrUserAlreadyMember là lỗi khi người dùng đã là thành viên
	ErrUserAlreadyMember ServiceError = "user_already_member"
	// ErrMaxTenantsReached là lỗi khi đã đạt số lượng tenant tối đa
	ErrMaxTenantsReached ServiceError = "max_tenants_reached"
	// ErrMaxUsersReached là lỗi khi đã đạt số lượng người dùng tối đa
	ErrMaxUsersReached ServiceError = "max_users_reached"
	// ErrInsufficientPermission là lỗi khi không đủ quyền
	ErrInsufficientPermission ServiceError = "insufficient_permission"
	// ErrDatabaseError là lỗi khi tương tác với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInvalidSortField là lỗi khi trường sắp xếp không hợp lệ
	ErrInvalidSortField ServiceError = "invalid_sort_field"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrTenantNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy tenant",
		ErrorCode:  "TENANT_NOT_FOUND",
	},
	ErrTenantAlreadyExists: {
		StatusCode: http.StatusConflict,
		Message:    "Tenant đã tồn tại",
		ErrorCode:  "TENANT_ALREADY_EXISTS",
	},
	ErrPlanNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy gói dịch vụ",
		ErrorCode:  "PLAN_NOT_FOUND",
	},
	ErrUserNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy người dùng",
		ErrorCode:  "USER_NOT_FOUND",
	},
	ErrUserAlreadyMember: {
		StatusCode: http.StatusConflict,
		Message:    "Người dùng đã là thành viên của tenant",
		ErrorCode:  "USER_ALREADY_MEMBER",
	},
	ErrMaxTenantsReached: {
		StatusCode: http.StatusForbidden,
		Message:    "Đã đạt số lượng tenant tối đa",
		ErrorCode:  "MAX_TENANTS_REACHED",
	},
	ErrMaxUsersReached: {
		StatusCode: http.StatusForbidden,
		Message:    "Đã đạt số lượng người dùng tối đa",
		ErrorCode:  "MAX_USERS_REACHED",
	},
	ErrInsufficientPermission: {
		StatusCode: http.StatusForbidden,
		Message:    "Không đủ quyền để thực hiện hành động này",
		ErrorCode:  "INSUFFICIENT_PERMISSION",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInvalidSortField: {
		StatusCode: http.StatusBadRequest,
		Message:    "Trường sắp xếp không hợp lệ",
		ErrorCode:  "INVALID_SORT_FIELD",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}
	
	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Tenant định nghĩa cấu trúc dữ liệu cho tenant
type Tenant struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name             string    `gorm:"column:name;size:100;not null" json:"name"`
	Slug             string    `gorm:"column:slug;size:100;uniqueIndex" json:"slug"`
	Description      string    `gorm:"column:description;size:500" json:"description"`
	PlanID           int       `gorm:"column:plan_id;not null" json:"plan_id"`
	OwnerUserID      int64     `gorm:"column:owner_user_id;not null" json:"owner_user_id"`
	IsActive         bool      `gorm:"column:is_active;default:true" json:"is_active"`
	Logo             string    `gorm:"column:logo;size:255" json:"logo"`
	StorageUsed      int64     `gorm:"column:storage_used;default:0" json:"storage_used"`
	StorageLimit     int64     `gorm:"column:storage_limit" json:"storage_limit"`
	BandwidthUsed    int64     `gorm:"column:bandwidth_used;default:0" json:"bandwidth_used"`
	BandwidthLimit   int64     `gorm:"column:bandwidth_limit" json:"bandwidth_limit"`
	ExpiresAt        *time.Time `gorm:"column:expires_at" json:"expires_at"`
	Settings         string    `gorm:"column:settings;type:json" json:"settings"`
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Tenant) TableName() string {
	return "tenant_tenants"
}

// TenantUser định nghĩa cấu trúc dữ liệu cho quan hệ giữa tenant và user
type TenantUser struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TenantID  int64     `gorm:"column:tenant_id;index:idx_tenant_user,unique:true" json:"tenant_id"`
	UserID    int64     `gorm:"column:user_id;index:idx_tenant_user,unique:true" json:"user_id"`
	Role      string    `gorm:"column:role;size:50;not null" json:"role"`
	IsActive  bool      `gorm:"column:is_active;default:true" json:"is_active"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (TenantUser) TableName() string {
	return "tenant_users"
}

// Plan định nghĩa cấu trúc dữ liệu cho gói dịch vụ
type Plan struct {
	ID               int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name             string    `gorm:"column:name;size:100;not null" json:"name"`
	Description      string    `gorm:"column:description;size:500" json:"description"`
	Price            float64   `gorm:"column:price;not null" json:"price"`
	PriceYearly      float64   `gorm:"column:price_yearly" json:"price_yearly"`
	Currency         string    `gorm:"column:currency;size:3;default:VND" json:"currency"`
	MaxUsers         int       `gorm:"column:max_users" json:"max_users"`
	StorageLimit     int64     `gorm:"column:storage_limit" json:"storage_limit"`
	BandwidthLimit   int64     `gorm:"column:bandwidth_limit" json:"bandwidth_limit"`
	Features         string    `gorm:"column:features;type:json" json:"features"`
	IsActive         bool      `gorm:"column:is_active;default:true" json:"is_active"`
	IsPublic         bool      `gorm:"column:is_public;default:true" json:"is_public"`
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Plan) TableName() string {
	return "tenant_plans"
}

// Repository định nghĩa interface cho tenant repository
type Repository interface {
	// Tenant methods
	CreateTenant(ctx context.Context, tenant *Tenant) error
	GetTenantByID(ctx context.Context, id int64) (*Tenant, error)
	GetTenantBySlug(ctx context.Context, slug string) (*Tenant, error)
	UpdateTenant(ctx context.Context, tenant *Tenant) error
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]Tenant, int64, error)
	GetTenantsByUserID(ctx context.Context, userID int64) ([]Tenant, error)
	
	// Tenant user methods
	AddUserToTenant(ctx context.Context, tenantUser *TenantUser) error
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	UpdateUserRole(ctx context.Context, tenantID, userID int64, role string) error
	GetTenantUsers(ctx context.Context, tenantID int64) ([]TenantUser, error)
	GetUserRole(ctx context.Context, tenantID, userID int64) (string, error)
	
	// Plan methods
	CreatePlan(ctx context.Context, plan *Plan) error
	GetPlanByID(ctx context.Context, id int) (*Plan, error)
	UpdatePlan(ctx context.Context, plan *Plan) error
	DeletePlan(ctx context.Context, id int) error
	ListPlans(ctx context.Context, params dto.ListPlansParams) ([]Plan, int64, error)
	GetActivePlans(ctx context.Context) ([]Plan, error)
}

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	// Tenant methods
	CreateTenant(ctx context.Context, req dto.CreateTenantRequest) (*dto.TenantResponse, error)
	GetTenant(ctx context.Context, id int64) (*dto.TenantResponse, error)
	GetTenantBySlug(ctx context.Context, slug string) (*dto.TenantResponse, error)
	UpdateTenant(ctx context.Context, id int64, req dto.UpdateTenantRequest) (*dto.TenantResponse, error)
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) (*dto.ListTenantsResponse, error)
	GetUserTenants(ctx context.Context, userID int64) (*dto.UserTenantsResponse, error)
	
	// Tenant user methods
	AddUserToTenant(ctx context.Context, req dto.AddUserRequest) (*dto.TenantUserResponse, error)
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	UpdateUserRole(ctx context.Context, tenantID, userID int64, role string) (*dto.TenantUserResponse, error)
	GetTenantUsers(ctx context.Context, tenantID int64) (*dto.TenantUsersResponse, error)
	
	// Plan methods
	CreatePlan(ctx context.Context, req dto.CreatePlanRequest) (*dto.PlanResponse, error)
	GetPlan(ctx context.Context, id int) (*dto.PlanResponse, error)
	UpdatePlan(ctx context.Context, id int, req dto.UpdatePlanRequest) (*dto.PlanResponse, error)
	DeletePlan(ctx context.Context, id int) error
	ListPlans(ctx context.Context, params dto.ListPlansParams) (*dto.ListPlansResponse, error)
	GetActivePlans(ctx context.Context) (*dto.ActivePlansResponse, error)
}
