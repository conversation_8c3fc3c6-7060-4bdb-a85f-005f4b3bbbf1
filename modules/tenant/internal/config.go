package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// TenantConfig chứa cấu hình tenant service
type TenantConfig struct {
	MaxTenantsPerUser     int    `env:"MAX_TENANTS_PER_USER" envDefault:"5"`
	MaxUsersPerTenant     int    `env:"MAX_USERS_PER_TENANT" envDefault:"50"`
	DefaultPlanID         int    `env:"DEFAULT_PLAN_ID" envDefault:"1"`
	EnableMultiTenancy    bool   `env:"ENABLE_MULTI_TENANCY" envDefault:"true"`
	DefaultStorageLimit   int64  `env:"DEFAULT_STORAGE_LIMIT" envDefault:"104857600"` // 100MB in bytes
	DefaultBandwidthLimit int64  `env:"DEFAULT_BANDWIDTH_LIMIT" envDefault:"1073741824"` // 1GB in bytes
	StoragePath           string `env:"STORAGE_PATH" envDefault:"./storage/tenants"`
	Message               string `env:"MESSAGE" envDefault:"Xin chào từ module Tenant!"`
}

// LoadTenantConfig đọc cấu hình tenant từ biến môi trường
func LoadTenantConfig() (*TenantConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := GetDefaultConfig()

	// Đọc cấu hình từ biến môi trường với prefix TENANT_
	opts := env.Options{
		Prefix: "TENANT_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình tenant từ biến môi trường: %w", err)
	}

	return cfg, nil
}

// GetDefaultConfig trả về cấu hình mặc định
func GetDefaultConfig() *TenantConfig {
	return &TenantConfig{
		MaxTenantsPerUser:     5,
		MaxUsersPerTenant:     50,
		DefaultPlanID:         1,
		EnableMultiTenancy:    true,
		DefaultStorageLimit:   104857600,  // 100MB in bytes
		DefaultBandwidthLimit: 1073741824, // 1GB in bytes
		StoragePath:           "./storage/tenants",
		Message:               "Xin chào từ module Tenant!",
	}
}
