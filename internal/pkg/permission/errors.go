// internal/pkg/permission/errors.go
package permission

// Đ<PERSON><PERSON> là phiên bản stub của file errors.go
// File này sẽ được hoàn thiện trong Task 02: Triển khai Xử lý Lỗi cho Hệ thống Phân quyền

import (
	"github.com/gin-gonic/gin"
)

// AbortWithAuthRequired dừng request và trả về lỗi 401 yêu cầu xác thực
// Stub - sẽ được triển khai đầy đủ trong Task 02
func AbortWithAuthRequired(c *gin.Context) {
	c.AbortWithStatus(401)
}

// AbortWithTenantRequired dừng request và trả về lỗi yêu cầu tenant
// Stub - sẽ được triển khai đầy đủ trong Task 02
func AbortWithTenantRequired(c *gin.Context) {
	c.AbortWithStatus(400)
}

// AbortWithPermissionDenied dừng request và trả về lỗi 403 từ chối quyền truy cập
// Stub - sẽ được triển khai đầy đủ trong Task 02
func AbortWithPermissionDenied(c *gin.Context, permission string) {
	c.AbortWithStatus(403)
}

// AbortWithPermissionCheckError dừng request và trả về lỗi 500 do lỗi hệ thống khi kiểm tra quyền
// Stub - sẽ được triển khai đầy đủ trong Task 02
func AbortWithPermissionCheckError(c *gin.Context, err error) {
	c.AbortWithStatus(500)
}
