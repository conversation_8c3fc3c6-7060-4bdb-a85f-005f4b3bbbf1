// internal/pkg/permission/factory.go
package permission

import (
	"github.com/gin-gonic/gin"
)

// MiddlewareFactory tạo ra các middleware functions để kiểm tra quyền.
// Nó implement PermissionMiddleware interface.
// Đây là phiên bản stub, sẽ được triển khai đầy đủ trong Task 03
type MiddlewareFactory struct {
	checker PermissionChecker
}

// NewMiddlewareFactory là constructor cho MiddlewareFactory.
// Stub - sẽ được triển khai đầy đủ trong Task 03
func NewMiddlewareFactory(checker PermissionChecker) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
	}
}

// RequirePermission tạo middleware kiểm tra một quyền cụ thể.
// Stub - sẽ được triển khai đầy đủ trong Task 03
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub - sẽ được triển khai đầy đủ trong Task 03
		c.Next()
	}
}

// RequireAnyPermission tạo middleware kiểm tra người dùng có ít nhất một trong các quyền được liệt kê.
// Stub - sẽ được triển khai đầy đủ trong Task 03
func (mf *MiddlewareFactory) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub - sẽ được triển khai đầy đủ trong Task 03
		c.Next()
	}
}

// RequireAllPermissions tạo middleware kiểm tra người dùng có tất cả các quyền được liệt kê.
// Stub - sẽ được triển khai đầy đủ trong Task 03
func (mf *MiddlewareFactory) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub - sẽ được triển khai đầy đủ trong Task 03
		c.Next()
	}
}
